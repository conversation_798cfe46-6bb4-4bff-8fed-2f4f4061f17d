using Newtonsoft.Json;
using QuantConnect.Brokerages;
using QuantConnect.Data;
using QuantConnect.Data.Consolidators;
using QuantConnect.Data.Market;
using QuantConnect.Indicators;
using QuantConnect.Orders;
using QuantConnect.Orders.Fees;
using QuantConnect.Securities;
using QuantConnect.Securities.CryptoFuture;
using QuantConnect.Util;
using System.Collections.Concurrent;
using System.IO;

namespace QuantConnect.Algorithm.CSharp {
  public class cCryptoBotAlgorithm: QCAlgorithm {
    private string _cryptoSymbol = null!;
    private decimal _mySize = 0.639m; // avoid `Insufficient buying power to complete orders` which might occur because of `adjustment` based on `bid` or `ask`
    private decimal _myFees = 0.00m / 100m;
    private decimal _myLeverage = 0m;
    private decimal _minLadderGap = 0;
    SymbolProperties _symbolProperties = null!;
    private decimal _minimumOrderSize => _symbolProperties.MinimumOrderSize ?? 23m;
    private int _priceDecimalPlaces = 0;
    private string _priceDecimalFormat = null!;
    private string _positionDecimalFormat = null!;
    private Symbol _qcSymbol = null!;
    private int _barIndex = -1;
    private decimal? _bid;
    private decimal? _ask;
    private decimal _trade;
    private int _openingPosition;
    private Random _random = new Random();
    private Dictionary<string, decimal> _tradingParams = null!;
    private EntryLadderInfo[] _entryLaddering = null!;
    private ExitLadderInfo[] _tpLaddering = null!;
    private ExitLadderInfo[] _slLaddering = null!;
    private ConcurrentQueue<(OrderEvent, Dictionary<string, object>)> _orderEventQueue = new();
    private TickConsolidator _consolidator = null!;
    private RollingWindow<TradeBar> _rollingConsolidatedBar = null!;

    private AverageTrueRange _atr = null!;
    private ISignalGenerator _signalGenerator = null!;
    // New state for sequential order processing

    private const string STOP_LOSS_AFTER_TP_REASON = "Protective StopLoss Order placed near the take-profit fill price after order cleanup.";
    private const string POSITION_IS_FULLY_CLOSED_REASON = "Try to cancel all pending Orders, because Position fully closed";
    private OrderState? _currentOrder;
    private OrderState? CurrentOrder {
      get => _currentOrder;
      set {
        _currentOrder = value;
      }
    }
    private bool _waitingForStopLossCancellation = false;
    private HashSet<int> _pendingStopLossCancellations = new HashSet<int>();
    private ExitLadderInfo? _pendingStopLossAfterTP = null;

    private cCryptoBot.Testing _testing = null!;
    private int _tradeMode = -1;
    private decimal _entryPrice = 0m;

    private Queue<(string Property, int LadderIndex, int EntryIndex, decimal Quantity, decimal Price, bool IsStopLimit, string Reason)> _pendingOrders { get; } = new Queue<(string, int, int, decimal, decimal, bool, string)>();
    private Queue<(int OrderId, Dictionary<string, object> TagDict, string Reason)> _pendingCancelRequests { get; } = new Queue<(int, Dictionary<string, object>, string)>();

    private class OrderState {
      public OrderTicket Ticket { get; set; } = null!;
      public string Property { get; set; } = null!; // e.g., OpenPosition, StopLossClosePosition
      public int LadderIndex { get; set; }          // For SL/TP ladders
      public int EntryIndex { get; set; }           // For entry orders
      public DateTime SubmitTime { get; set; }
      public OrderStatus LastStatus { get; set; }
    }

    public override void Initialize() {
      SetTimeZone(TimeZones.Utc);
      var currency = GetParameter("currency", "xDummyError");
      SetAccountCurrency(currency);

      _tradingParams = new Dictionary<string, decimal> {
        { "atr_sl_ratio", 1.25m },
        { "atr_tp_ratio", 3.25m },
        { "n_entries", 1m },
        { "num_step_entries", 2m },
        { "sl_levels", 1m },
        { "tp_levels", 3m }
      };

      _entryLaddering = new EntryLadderInfo[(int)_tradingParams["n_entries"]];
      _tpLaddering = new ExitLadderInfo[(int)_tradingParams["tp_levels"]];
      _slLaddering = new ExitLadderInfo[(int)_tradingParams["sl_levels"]];

      _tradeMode = GetParameter("tradeMode", -1); // 0 - SpotMargin, 1 - Futures, 2 - Mexc

      if (_tradeMode != 0 && _tradeMode != 1 && _tradeMode != 2) {
        throw new ArgumentException($"Invalid tradeMode: {_tradeMode}. Use 0 for SpotMargin, 1 for Futures, or 2 for MEXC Futures.");
      }

      if (_tradeMode == 0) {
        SetBrokerageModel(BrokerageName.Binance, AccountType.Margin);
        Log("Trade Mode: SpotMargin - Using Binance Spot/Margin");
      } else if (_tradeMode == 1) {
        SetBrokerageModel(BrokerageName.BinanceFutures, AccountType.Margin);
        Log("Trade Mode: Futures - Using Binance Futures");
      }

      _myLeverage = GetParameter("leverage", _tradeMode == 0 ? 1m : 3m);
      Log($"Leverage parameter: {_myLeverage}");

      var ticker = GetParameter("symbol", "xDummyError");
      ticker += currency;
      Log($"Ticker parameter: {ticker}");

      Security crypto;
      if (_tradeMode == 0) {
        var spotCrypto = AddCrypto(ticker, Resolution.Tick, leverage: _myLeverage);
        crypto = spotCrypto;
        Log($"Added SpotMargin crypto: {crypto.Symbol}, Type: {crypto.Type}, Base currency: {spotCrypto.BaseCurrency?.Symbol ?? "null"}");
      } else if (_tradeMode == 1) {
        var cryptoFuture = AddCryptoFuture(ticker, Resolution.Tick, leverage: _myLeverage);
        crypto = cryptoFuture;

        Log($"Added crypto future: {crypto.Symbol}, Type: {crypto.Type}, Base currency: {cryptoFuture.BaseCurrency?.Symbol ?? "null"}");
      } else if (_tradeMode == 2) {
        SetBrokerageModel(BrokerageName.MEXCFutures, AccountType.Margin);
        var cryptoFuture = AddCryptoFuture(ticker, Resolution.Tick, leverage: _myLeverage);
        crypto = cryptoFuture;
        Log("Trade Mode: MEXC Futures - Using MEXC Futures");
        Log($"Added MEXC crypto future: {crypto.Symbol}, Type: {crypto.Type}, Base currency: {cryptoFuture.BaseCurrency?.Symbol ?? "null"}");
      } else {
        throw new ArgumentException($"Invalid tradeMode: {_tradeMode}. Use 0 for SpotMargin, 1 for Futures, or 2 for MEXC Futures.");
      }

      _symbolProperties = crypto.SymbolProperties;

      if (_symbolProperties.MinimumOrderSize == null) {
        Log($"MinimumOrderSize was null, using default value of 25m");
      }

      Log($"Symbol properties - MarketTicker: {_symbolProperties.MarketTicker}, QuoteCurrency: {_symbolProperties.QuoteCurrency}, MinimumPriceVariation: {_symbolProperties.MinimumPriceVariation}, LotSize: {_symbolProperties.LotSize}, MinimumOrderSize: {_minimumOrderSize}");

      _minLadderGap = 3 * _symbolProperties.MinimumPriceVariation;
      Log($"Minimum ladder gap: {_minLadderGap} (3 * {_symbolProperties.MinimumPriceVariation})");

      _cryptoSymbol = _symbolProperties.MarketTicker.Replace(_symbolProperties.QuoteCurrency, "");
      Log($"Extracted crypto symbol: '{_cryptoSymbol}' (from MarketTicker: '{_symbolProperties.MarketTicker}' - QuoteCurrency: '{_symbolProperties.QuoteCurrency}')");

      _priceDecimalPlaces = Math.Max(0, (int)-Math.Log10((double)_symbolProperties.MinimumPriceVariation));
      _priceDecimalFormat = $"F{_priceDecimalPlaces}"; // Creates "F2", "F5"

      var precision = _symbolProperties.LotSize < 1 ? (int)-Math.Log10((double)_symbolProperties.LotSize) : 0;
      _positionDecimalFormat = $"F{precision}"; // Creates "F2", "F5" based on LotSize

      crypto.SetLeverage(_myLeverage);
      if (LiveMode) {
        _qcSymbol = crypto.Symbol;
        SetWarmUp(TimeSpan.FromMinutes(30));
      } else {
        SetStartDate(2025, 3, 4);
        SetEndDate(2025, 3, 6);
        SetCash(_symbolProperties.QuoteCurrency, 100);
        var security = AddData<CustomCryptoData>(ticker, crypto.SymbolProperties, crypto.Exchange.Hours, Resolution.Tick, fillForward: false, leverage: _myLeverage);
        _qcSymbol = security.Symbol;

        security.SetFeeModel(new VillaFeeModel());
        SetBenchmark(ticker);
      }

      _consolidator = new TickConsolidator(TimeSpan.FromMinutes(1));
      _consolidator.DataConsolidated += BarHandler;
      SubscriptionManager.AddConsolidator(_qcSymbol, _consolidator);

      _rollingConsolidatedBar = new RollingWindow<TradeBar>(50);

      _atr = new AverageTrueRange("ATR(7)", 7, MovingAverageType.Wilders);
      _atr.Window.Size = 50;
      RegisterIndicator(_qcSymbol, _atr, _consolidator);

      _signalGenerator = new EMABollingerSignalGenerator();
      _signalGenerator.Initialize(_qcSymbol, _consolidator, this);

      Schedule.On(DateRules.EveryDay(), TimeRules.Every(TimeSpan.FromMilliseconds(1)), ProcessOrderEventQueue);
      _pendingStopLossAfterTP = null;

      _testing = new cCryptoBot.Testing(this, _qcSymbol, _symbolProperties);
    }

    public override void OnData(Slice slice) {
      if (!slice.Ticks.ContainsKey(_qcSymbol))
        return;

      foreach (var tick in slice.Ticks[_qcSymbol]) {
        if (tick.TickType == TickType.Trade) {
          _trade = tick.LastPrice;
          // Log($"xxx C# barIdx={_barIndex}, {tick.Time:yyyy-MM-dd HH:mm:ss.fff}, {_trade}, {tick.Quantity}");
        } else if (tick.TickType == TickType.Quote) {
          _bid = tick.BidPrice;
          _ask = tick.AskPrice;
        }
      }
      if (!LiveMode) {
        _bid = _trade;
        _ask = _trade;
      }
    }

    public override void OnWarmupFinished() {
      var position = Portfolio[_qcSymbol].Quantity;
      var openOrders = Transactions.GetOpenOrders(_qcSymbol);

      if (Math.Abs(position) >= 1e-5m)
        throw new Exception($"Warmup finished with non-zero position: {position.ToString(_positionDecimalFormat)}");
      if (openOrders.Count > 0)
        throw new Exception($"Warmup finished with {openOrders.Count} open order(s): {string.Join(", ", openOrders.Select(o => o.ToString()))}");
      var cryptoBalance = Portfolio.CashBook[_cryptoSymbol].Amount;
      if (Math.Abs(cryptoBalance) >= 1e-8m)
        throw new Exception($"Warming up finished with non-zero CASH balance: {cryptoBalance}");
      Log($"on_warmup_finished - Portfolio: {Portfolio[_qcSymbol]}, No open orders confirmed.");
    }

    public override void OnOrderEvent(OrderEvent orderEvent) {
      var ticket = orderEvent.Ticket;
      var rawTag = ticket.Tag;
      Dictionary<string, object> tagDict;

      try {
        var jsonStart = rawTag.IndexOf('{');
        var jsonEnd = rawTag.LastIndexOf('}') + 1;
        var jsonStr = (jsonStart != -1 && jsonEnd != 0 && jsonEnd > jsonStart)
                        ? rawTag.Substring(jsonStart, jsonEnd - jsonStart)
                        : rawTag;
        tagDict = JsonConvert.DeserializeObject<Dictionary<string, object>>(jsonStr) ?? new Dictionary<string, object>();
      } catch (Exception ex) {
        Log($"Error deserializing tag '{rawTag}' for order {orderEvent.OrderId}: {ex.Message}");
        tagDict = new Dictionary<string, object>();
      }

      DateTime submitTime;
      if (tagDict.TryGetValue("SubmitTime", out var submitTimeObj) &&
          submitTimeObj is string submitTimeStr &&
          DateTime.TryParse(submitTimeStr, out submitTime)) {
      } else {
        submitTime = DateTime.UtcNow;
        Log($"Could not parse SubmitTime from tag for order {orderEvent.OrderId}. Using UtcNow.");
      }

      var curUtc = DateTime.UtcNow;
      var timeDiff = (curUtc - submitTime).TotalSeconds;

      var cryptoBalance = Portfolio.CashBook[_cryptoSymbol].Amount;
      var position = Portfolio[_qcSymbol].Quantity;
      var order = Transactions.GetOrderById(orderEvent.OrderId);

      try {
        // Build log prefix with defensive checks for each potentially null field
        string bidStr, askStr, brokerIdStr, propertyStr;
        
        // Check bid/ask
        if (_bid.HasValue && _ask.HasValue) {
          bidStr = _bid.Value.ToString(_priceDecimalFormat);
          askStr = _ask.Value.ToString(_priceDecimalFormat);
        } else {
          bidStr = _bid?.ToString(_priceDecimalFormat) ?? "null";
          askStr = _ask?.ToString(_priceDecimalFormat) ?? "null";
          Log($"DEBUG OnOrderEvent OID={orderEvent.OrderId} Status={orderEvent.Status}: bid={bidStr}, ask={askStr} (one or both null)");
        }
        
        // Check order and BrokerId
        if (order == null) {
          brokerIdStr = "ORDER_NULL";
          Log($"DEBUG OnOrderEvent OID={orderEvent.OrderId} Status={orderEvent.Status}: Transactions.GetOrderById returned null");
        } else if (order.BrokerId == null) {
          brokerIdStr = "BROKERID_NULL";
          Log($"DEBUG OnOrderEvent OID={orderEvent.OrderId} Status={orderEvent.Status}: order.BrokerId is null");
        } else {
          brokerIdStr = string.Join(",", order.BrokerId);
        }
        
        // Check property in tagDict
        if (!tagDict.TryGetValue("property", out var propertyObj) || propertyObj == null) {
          propertyStr = "PROPERTY_NULL";
          Log($"DEBUG OnOrderEvent OID={orderEvent.OrderId} Status={orderEvent.Status}: tagDict missing 'property' key or value is null");
        } else {
          propertyStr = propertyObj.ToString() ?? "PROPERTY_TOSTRING_NULL";
        }

        var logPrefix = $"barIdx={_barIndex}, crypto={cryptoBalance}, position={position}, TimeDiff({orderEvent.Status})={timeDiff:F3}s, " +
                        $"QTY={orderEvent.Quantity}, OID={orderEvent.OrderId}, " +
                        $"{{bid={bidStr},ask={askStr}}}, " +
                        $"BID={brokerIdStr}, stopPrice={orderEvent.StopPrice?.ToString(_priceDecimalFormat) ?? "null"}, " +
                        $"limitPrice={orderEvent.LimitPrice?.ToString(_priceDecimalFormat) ?? "null"}, fillPrice={orderEvent.FillPrice.ToString(_priceDecimalFormat)}, " + 
                        $"property={propertyStr}";

        bool flag = (orderEvent.Status == OrderStatus.Filled && propertyStr != "PROPERTY_NULL" && propertyStr != "PROPERTY_TOSTRING_NULL" && 
                     (propertyStr == "StopLossClosePosition" || propertyStr == "TakeProfitClosePosition"));
        if (!flag) {
          Log(logPrefix);
        } else {
          var latestProfitLoss = 0m;
          var transactionRecord = Transactions.TransactionRecord;
          if (transactionRecord.Any()) {
            latestProfitLoss = transactionRecord.OrderByDescending(x => x.Key).First().Value;
          }

          var reason = tagDict.TryGetValue("Reason", out var reasonObj) && reasonObj != null ? reasonObj.ToString() : null;
          var message = !string.IsNullOrEmpty(orderEvent.Message) ? orderEvent.Message : null;
          var baseLog = $"{logPrefix}, OrderFee={orderEvent.OrderFee.ToString()}, ProfitLoss={latestProfitLoss.ToString(_priceDecimalFormat)}";
          
          if (!string.IsNullOrEmpty(reason) && !string.IsNullOrEmpty(message)) {
            Log($"{baseLog}, Reason=`{reason}`, Message=`{message}`");
          } else if (!string.IsNullOrEmpty(reason)) {
            Log($"{baseLog}, Reason=`{reason}`");
          } else if (!string.IsNullOrEmpty(message)) {
            Log($"{baseLog}, Message=`{message}`");
          } else {
            Log(baseLog);
          }
        }

        tagDict["enqueue_time"] = GetUtcTime();
        _orderEventQueue.Enqueue((orderEvent, tagDict));
      } catch (Exception ex) {
        Log($"ERROR OnOrderEvent OID={orderEvent.OrderId} Status={orderEvent.Status}: Exception in log building/enqueue: {ex.Message}");
        Log($"DEBUG State: _bid={_bid}, _ask={_ask}, order={(order == null ? "null" : "not-null")}, " +
            $"order.BrokerId={(order?.BrokerId == null ? "null" : "not-null")}, tagDict.Count={tagDict.Count}");
        
        // Still try to enqueue with minimal data
        try {
          tagDict["enqueue_time"] = GetUtcTime();
          _orderEventQueue.Enqueue((orderEvent, tagDict));
        } catch (Exception ex2) {
          Log($"CRITICAL OnOrderEvent OID={orderEvent.OrderId}: Failed to enqueue even with fallback: {ex2.Message}");
        }
      }
    }

    private (bool hasStopLoss, bool hasTakeProfit) CheckExistingProtectiveOrders() {
      var openOrders = Transactions.GetOpenOrders(_qcSymbol);
      bool hasStopLoss = false;
      bool hasTakeProfit = false;

      foreach (var order in openOrders) {
        try {
          var tagDict = JsonConvert.DeserializeObject<Dictionary<string, object>>(order.Tag) ?? new Dictionary<string, object>();

          if (tagDict.TryGetValue("property", out var propObj)) {
            var property = propObj?.ToString();
            if (property == "StopLossClosePosition") {
              hasStopLoss = true;
            } else if (property == "TakeProfitClosePosition") {
              hasTakeProfit = true;
            }
          }
        } catch (Exception ex) {
          Log($"Error deserializing tag '{order.Tag}' for order {order.Id} during CheckExistingProtectiveOrders: {ex.Message}");
        }
      }

      var pendingStopLoss = _pendingOrders.Any(o => o.Property == "StopLossClosePosition");
      var pendingTakeProfit = _pendingOrders.Any(o => o.Property == "TakeProfitClosePosition");

      hasStopLoss = hasStopLoss || pendingStopLoss;
      hasTakeProfit = hasTakeProfit || pendingTakeProfit;

      Log($"Existing protective orders check: StopLoss={hasStopLoss} (open orders + pending), TakeProfit={hasTakeProfit} (open orders + pending)");
      return (hasStopLoss, hasTakeProfit);
    }

    private void ProcessOrderEventQueue() {
      var bufferedEvents = new List<(OrderEvent, Dictionary<string, object>)>();

      while (_orderEventQueue.TryDequeue(out var queueItem)) {
        var (orderEvent, tagDict) = queueItem;

        if (CurrentOrder != null && orderEvent.Ticket.OrderId != CurrentOrder.Ticket.OrderId) {
          bufferedEvents.Add((orderEvent, tagDict));
          continue;
        }

        if (!tagDict.TryGetValue("property", out var propertyObj) || !(propertyObj is string orderProperty)) {
          Log($"Order event {orderEvent.OrderId} missing 'property' in tag. Skipping.");
          continue;
        }

        var position = Portfolio[_qcSymbol].Quantity;
        if (CurrentOrder != null)
          CurrentOrder.LastStatus = orderEvent.Status;

        DateTime enqueueTime;
        if (tagDict.TryGetValue("enqueue_time", out var enqueueTimeObj) &&
            enqueueTimeObj is string enqueueTimeStr &&
            DateTime.TryParse(enqueueTimeStr, out enqueueTime)) {
          var dequeueTime = DateTime.UtcNow;
          var queueTimeMs = (dequeueTime - enqueueTime).TotalMilliseconds;
          var currentOrderInfo = CurrentOrder != null ? $" (CurrentOrder: OID={CurrentOrder.Ticket.OrderId})" : "";
          Log($"Order event processed: QueueCost={queueTimeMs:F2}ms, Property={orderProperty}, `{orderEvent.ShortToString()}`{currentOrderInfo}");
        } else {
          Log($"Could not parse enqueue_time from tag for order {orderEvent.ShortToString()}");
        }

        if (orderEvent.Status == OrderStatus.Invalid) {
          HandleInvalidOrder(orderEvent, tagDict, orderProperty);
          continue; // Continue processing other events
        }

        if (orderEvent.Status == OrderStatus.Filled) {
          if (orderProperty == "OpenPosition") {
            HandleOpenPosition(orderEvent, tagDict);
          } else if (orderProperty == "StopLossClosePosition" || orderProperty == "TakeProfitClosePosition") {
            HandleExitPosition(orderEvent, tagDict, orderProperty);
          }
          if (position == 0) {
            EnqueueCancelAllOrders(POSITION_IS_FULLY_CLOSED_REASON);
            _openingPosition = 0;
            _entryPrice = 0m;
            ResetLadders();
            _pendingOrders.Clear();
            _waitingForStopLossCancellation = false;
            _pendingStopLossCancellations.Clear();
            CurrentOrder = null;
          } else {
            Log($"Clearing CurrentOrder after handling {orderProperty} (OID={CurrentOrder?.Ticket.OrderId})");
            CurrentOrder = null;
          }
          continue; // Continue processing other events
        }

        if (orderEvent.Status == OrderStatus.PartiallyFilled) {
          // Ignore partial fills, wait for full fill or cancellation
          continue; // Continue processing other events
        }

        if (orderEvent.Status == OrderStatus.Canceled) {
          if (orderProperty == "StopLossClosePosition" && _pendingStopLossCancellations.Contains(orderEvent.OrderId)) {
            _pendingStopLossCancellations.Remove(orderEvent.OrderId);
            Log($"StopLoss order {orderEvent.OrderId} cancellation confirmed. Pending cancellations remaining: {_pendingStopLossCancellations.Count}");
            if (_pendingStopLossCancellations.Count == 0) {
              _waitingForStopLossCancellation = false;
              Log("All StopLoss cancellations completed. Ready to place new StopLoss order.");

              // Place the stored StopLoss order if one exists
              if (_pendingStopLossAfterTP.HasValue) {
                var slOrder = _pendingStopLossAfterTP.Value;
                _pendingOrders.Enqueue(("StopLossClosePosition", 0, 0, slOrder.Amount, slOrder.Price, true, STOP_LOSS_AFTER_TP_REASON));
                Log($"Enqueuing stored StopLoss order at price {slOrder.Price} after cancellations completed.");
                _pendingStopLossAfterTP = null;
              }
            }
          }
          CurrentOrder = null;
          continue; // Continue processing other events
        }

        if (orderEvent.Status == OrderStatus.Submitted) {
          CurrentOrder = null;
          continue; // Continue processing other events
        }
      }

      foreach (var bufferedEvent in bufferedEvents) {
        _orderEventQueue.Enqueue(bufferedEvent);
      }

      while (_pendingCancelRequests.Count > 0 && CurrentOrder == null) {
        var (orderId, tagDict, reason) = _pendingCancelRequests.Dequeue();
        var order = Transactions.GetOrderById(orderId);

        if (order.Status == OrderStatus.PartiallyFilled || order.Status == OrderStatus.Filled) {
          Log($"Order {orderId} is {order.Status}. Skipping cancellation and waiting for completion: {reason}");
          continue;
        }

        if (order.Status == OrderStatus.New) {
          Log($"Order {orderId} is in New status. Requeueing cancellation: {reason}");
          _pendingCancelRequests.Enqueue((orderId, tagDict, reason));
          continue;
        }

        var ticket = Transactions.GetOrderTicket(orderId);
        var cancelResult = ticket.Cancel(JsonConvert.SerializeObject(tagDict));
        if (!cancelResult.IsError) {
          Log($"Cancellation submitted for order {orderId}: {reason}");
          if (tagDict.TryGetValue("property", out var propObj) && propObj?.ToString() == "StopLossClosePosition") {
            _pendingStopLossCancellations.Add(orderId);
          }
        }
      }

      while (CurrentOrder == null && !_waitingForStopLossCancellation && _pendingOrders.Count > 0) {
        var (property, ladderInx, entryInx, quantity, price, isStopLimit, reason) = _pendingOrders.Dequeue();
        PlaceNextOrder(reason, property, ladderInx, entryInx, quantity, price, isStopLimit);
      }
    }

    private void HandleInvalidOrder(OrderEvent orderEvent, Dictionary<string, object> tagDict, string orderProperty) {
      _openingPosition = 0;
      var position = Portfolio[_qcSymbol].Quantity;

      if (position == 0) {
        Log("No position to adjust, invalid order ignored");
        CurrentOrder = null;
        return;
      }

      var retryWithOriginalParamsCodes = new HashSet<int> { -1021, -3006, -3007, -3044, -3045};
      bool retryWithOriginalParams = false;
      int? errorCode = null;

      try {
        var messageData = JsonConvert.DeserializeObject<Dictionary<string, object>>(orderEvent.Message);
        if (messageData != null) {
          if (messageData.TryGetValue("code", out var codeObj) && codeObj != null) {
            string? codeStr = codeObj.ToString();
            if (codeStr != null && int.TryParse(codeStr, out var code)) {
              errorCode = code;
              retryWithOriginalParams = retryWithOriginalParamsCodes.Contains(code);
            }
          }
        } else {
          Log($"Deserialized message data is null for message: {orderEvent.Message}");
        }
      } catch (JsonException) {
        Log($"Non-JSON error message received: `{orderEvent.Message}`");
      } catch (Exception ex) {
        Log($"Error processing error code from message: {orderEvent.Message}. Error: {ex.Message}");
      }

      var adjustment = _symbolProperties.MinimumPriceVariation * 2;
      var quantity = retryWithOriginalParams ? orderEvent.Quantity : -position;

      if (orderProperty == "StopLossClosePosition") {
        if (retryWithOriginalParams && orderEvent.LimitPrice.HasValue && orderEvent.StopPrice.HasValue) {
          var limitPrice = orderEvent.LimitPrice.Value;
          tagDict["Reason"] = $"Retrying StopLoss with original params, Error code: {errorCode ?? 0}";

          ClearOrdersByPropertyFromQueue(orderProperty);

          _pendingOrders.Enqueue((orderProperty, Convert.ToInt32(tagDict["LadderInx"]), 0, quantity, limitPrice, true, tagDict["Reason"]?.ToString() ?? "StopLoss retry"));
          Log($"Enqueued StopLoss LimitOrder retry barIdx={_barIndex}, QTY={quantity}, LimitPrice={limitPrice}, Reason=`{tagDict["Reason"]}`");
        } else {
          EnqueueCancelOrdersWithProperty(orderProperty, "Adjusting StopLoss ladder");

          var goLong = position > 0;
          decimal stopLossPrice = goLong ? _bid!.Value - adjustment : _ask!.Value + adjustment;

          ClearOrdersByPropertyFromQueue(orderProperty);

          _pendingOrders.Enqueue((orderProperty, 0, 0, -position, stopLossPrice, true, tagDict["Reason"]?.ToString() ?? "StopLoss retry"));
          Log($"Enqueued StopLoss LimitOrder retry barIdx={_barIndex}, QTY={quantity}, stopLossPrice={stopLossPrice}, Reason=`{tagDict["Reason"]}`");
        }
      } else if (orderProperty == "TakeProfitClosePosition") {
        if (retryWithOriginalParams && orderEvent.LimitPrice.HasValue) {
          var limitPrice = orderEvent.LimitPrice.Value;
          tagDict["Reason"] = $"Retrying TakeProfit with original params, Error code: {errorCode ?? 0}";

          ClearOrdersByPropertyFromQueue(orderProperty);
          _pendingOrders.Enqueue((orderProperty, Convert.ToInt32(tagDict["LadderInx"]), 0, quantity, limitPrice, false, tagDict["Reason"]?.ToString() ?? "TakeProfit retry"));
          Log($"Enqueued TakeProfit LimitOrder retry barIdx={_barIndex}, QTY={quantity}, LimitPrice={limitPrice}, Reason=`{tagDict["Reason"]}`");
        } else {
          EnqueueCancelOrdersWithProperty(orderProperty, "Adjusting TakeProfit ladder");
          var limitPrice = position > 0 ? _bid!.Value + adjustment : _ask!.Value - adjustment;
          tagDict["Reason"] = $"Invalid TakeProfit order retry (adjusted params), Error code: {errorCode ?? 0}";

          ClearOrdersByPropertyFromQueue(orderProperty);
          _pendingOrders.Enqueue((orderProperty, Convert.ToInt32(tagDict["LadderInx"]), 0, quantity, limitPrice, false, tagDict["Reason"]?.ToString() ?? "TakeProfit retry"));
          Log($"Enqueued TakeProfit LimitOrder retry barIdx={_barIndex}, QTY={quantity}, LimitPrice={limitPrice}, Reason=`{tagDict["Reason"]}`");
        }
      }

      CurrentOrder = null;
    }

    private void HandleOpenPosition(OrderEvent orderEvent, Dictionary<string, object> tagDict) {
      var position = Portfolio[_qcSymbol].Quantity;
      var nextSize = Convert.ToDecimal(tagDict["NextOrderSize"]);
      var initPosition = Convert.ToDecimal(tagDict["InitPosition"]);
      var entryPrice = orderEvent.FillPrice;
      _entryPrice = entryPrice;

      var barIdx = Convert.ToInt32(tagDict["barIdx"]);
      var entryIdx = Array.FindIndex(_entryLaddering, e => e.BarIndex == barIdx);
      _entryLaddering[entryIdx].PositionChanged = Math.Abs(position - initPosition);

      EnqueueCancelAllOrders("Setting up new position ladders");

      var n = _barIndex - _entryLaddering[0].BarIndex + 1;
      var avgAtr = _atr.Samples > 0 ? _atr.Take((int)Math.Min(n, _atr.Samples)).Select(idp => idp.Value).Average() : 0;
      var avgClose = _rollingConsolidatedBar.Take(Math.Min(n, _rollingConsolidatedBar.Count)).Average(b => b.Close);
      var longStopLoss = avgClose - _tradingParams["atr_sl_ratio"] * avgAtr;
      var longTakeProfit = avgClose + _tradingParams["atr_tp_ratio"] * avgAtr;
      var shortStopLoss = avgClose + _tradingParams["atr_sl_ratio"] * avgAtr;
      var shortTakeProfit = avgClose - _tradingParams["atr_tp_ratio"] * avgAtr;

      var slLevels = (int)_tradingParams["sl_levels"];
      var tpLevels = (int)_tradingParams["tp_levels"];
      var originalSlPrice = position > 0 ? longStopLoss : shortStopLoss;
      var originalTpPrice = position > 0 ? longTakeProfit : shortTakeProfit;
      
      // Check if original ATR prices satisfy PopulateLaddering requirements
      var slValid = PrecheckLaddering(entryPrice, originalSlPrice, slLevels);
      var tpValid = PrecheckLaddering(entryPrice, originalTpPrice, tpLevels);
      
      decimal adjustedSlPrice, adjustedTpPrice;
      
      if (slValid && tpValid) {
        // Original ATR prices satisfy requirements, use them directly
        adjustedSlPrice = originalSlPrice;
        adjustedTpPrice = originalTpPrice;
      } else {
        // Original prices don't satisfy requirements, apply adjustments
        var minRequiredSlGap = _minLadderGap * slLevels;
        var minRequiredTpGap = _minLadderGap * tpLevels;
        var atrRatio = _tradingParams["atr_tp_ratio"] / _tradingParams["atr_sl_ratio"];
        
        // Calculate original distances based on ATR from entry price
        var originalSlDistance = position > 0 
          ? entryPrice - longStopLoss 
          : shortStopLoss - entryPrice;
        var originalTpDistance = position > 0 
          ? longTakeProfit - entryPrice 
          : entryPrice - shortTakeProfit;
        
        // Find the larger distance and ensure minimum gap requirements
        var adjustedSlDistance = Math.Max(originalSlDistance, minRequiredSlGap);
        var adjustedTpDistance = Math.Max(originalTpDistance, minRequiredTpGap);
        
        // Apply ATR ratio relationship: use the larger distance as base, calculate the other
        if (adjustedSlDistance > adjustedTpDistance) {
          // SL distance is larger, calculate TP based on ratio
          adjustedTpDistance = Math.Max(adjustedSlDistance * atrRatio, minRequiredTpGap);
        } else {
          // TP distance is larger, calculate SL based on ratio  
          adjustedSlDistance = Math.Max(adjustedTpDistance / atrRatio, minRequiredSlGap);
        }
        
        adjustedSlPrice = position > 0 
          ? entryPrice - adjustedSlDistance
          : entryPrice + adjustedSlDistance;
        
        adjustedTpPrice = position > 0
          ? entryPrice + adjustedTpDistance
          : entryPrice - adjustedTpDistance;
      }

      _slLaddering = PopulateLaddering(
        -position,
        entryPrice,
        adjustedSlPrice,
        slLevels,
        _slLaddering,
        true);

      _tpLaddering = PopulateLaddering(
        -position,
        entryPrice,
        adjustedTpPrice,
        tpLevels,
        _tpLaddering,
        false);

      for (int i = 0; i < _slLaddering.Length; i++)
        _pendingOrders.Enqueue(("StopLossClosePosition", i, 0, _slLaddering[i].Amount, _slLaddering[i].Price, true, "Initial StopLoss ladder setup"));
      for (int i = 0; i < _tpLaddering.Length; i++)
        _pendingOrders.Enqueue(("TakeProfitClosePosition", i, 0, _tpLaddering[i].Amount, _tpLaddering[i].Price, false, "Initial TakeProfit ladder setup"));

      CurrentOrder = null;
    }

    private void HandleExitPosition(OrderEvent orderEvent, Dictionary<string, object> tagDict, string orderProperty) {
      var ladderInx = Convert.ToInt32(tagDict["LadderInx"]);
      var position = Portfolio[_qcSymbol].Quantity;
      var ladder = orderProperty == "StopLossClosePosition" ? _slLaddering : _tpLaddering;

      _openingPosition = 0;
      var remainingPosition = Math.Abs(position);

      if (remainingPosition == 0) {
        Log("Position fully closed, exiting HandleExitPosition");
        return;
      }

      if (orderProperty == "TakeProfitClosePosition") {
        // Update the TP ladder with actual fill price for future reference
        _tpLaddering[ladderInx].Price = orderEvent.FillPrice;
        Log($"Updated TP ladder {ladderInx} with actual fill price: {orderEvent.FillPrice.ToString(_priceDecimalFormat)}");

        var openOrders = Transactions.GetOpenOrders();
        _pendingStopLossCancellations.Clear();
        foreach (var order in openOrders) {
          try {
            var orderTagDict = JsonConvert.DeserializeObject<Dictionary<string, object>>(order.Tag) ?? new Dictionary<string, object>();

            if (orderTagDict.TryGetValue("property", out var propObj) && propObj?.ToString() == "StopLossClosePosition") {
              orderTagDict["Reason"] = "Take-profit triggered, canceling all stop-loss orders";
              _pendingCancelRequests.Enqueue((order.Id, orderTagDict, orderTagDict["Reason"]?.ToString() ?? "Take-profit triggered"));
              _pendingStopLossCancellations.Add(order.Id);
            }
          } catch (Exception ex) {
            Log($"Error deserializing tag '{order.Tag}' for order {order.Id} during HandleExitPosition: {ex.Message}");
          }
        }

        if (_pendingStopLossCancellations.Count > 0) {
          _waitingForStopLossCancellation = true;
          Log($"Initiated cancellation of {_pendingStopLossCancellations.Count} StopLoss orders. Waiting for confirmation before placing new StopLoss.");
        } else {
          _waitingForStopLossCancellation = false;
          Log("No StopLoss orders found to cancel. Proceeding to set new StopLoss.");
        }

        var isLong = position > 0;
        decimal newStopLossPrice;

        if (ladderInx == 0) {
          newStopLossPrice = isLong
            ? _entryPrice + _minLadderGap
            : _entryPrice - _minLadderGap;
          Log($"First TP level hit, setting StopLoss to entry price ± 1 tick: {newStopLossPrice.ToString(_priceDecimalFormat)} (entry: {_entryPrice.ToString(_priceDecimalFormat)})");
        } else {
          var previousTpActualPrice = _tpLaddering[ladderInx - 1].Price;
          newStopLossPrice = isLong
            ? previousTpActualPrice + _minLadderGap
            : previousTpActualPrice - _minLadderGap;
          Log($"TP level {ladderInx + 1} hit, setting StopLoss to previous TP actual fill price ± 1 tick: {newStopLossPrice.ToString(_priceDecimalFormat)} (previous TP actual: {previousTpActualPrice.ToString(_priceDecimalFormat)})");
        }

        _slLaddering = new ExitLadderInfo[1];
        _slLaddering[0] = new ExitLadderInfo { Price = newStopLossPrice, Amount = -position };
        Log($"Prepared new StopLoss order at price {newStopLossPrice.ToString(_priceDecimalFormat)} after TakeProfit fill, remaining position: {position}");

        if (_waitingForStopLossCancellation) {
          // Store the pending StopLoss order for after cancellations complete
          _pendingStopLossAfterTP = _slLaddering[0];
        } else {
          ClearOrdersByPropertyFromQueue("StopLossClosePosition");
          _pendingOrders.Enqueue(("StopLossClosePosition", 0, 0, _slLaddering[0].Amount, _slLaddering[0].Price, true, "StopLoss after TakeProfit fill"));
          Log($"Enqueued new StopLossClosePosition order at price {_slLaddering[0].Price}, quantity {_slLaddering[0].Amount}");
        }

        var tpUnfilled = new List<(decimal Price, decimal Amount)>();

        foreach (var order in openOrders) {
          var orderTagDict = JsonConvert.DeserializeObject<Dictionary<string, object>>(order.Tag) ?? new Dictionary<string, object>();
          if (orderTagDict.TryGetValue("property", out var propObj) && propObj?.ToString() == "TakeProfitClosePosition") {
            decimal price = order.Type == OrderType.Limit ? ((LimitOrder)order).LimitPrice : 0;
            decimal amount = Math.Abs(order.Quantity);
            tpUnfilled.Add((price, amount));
          }
        }

        var reason = tagDict.TryGetValue("Reason", out var reasonObj) && reasonObj != null ? reasonObj.ToString() : null;
        if (tpUnfilled.Count > 0) {
          decimal totalTpUnfilledAmount = tpUnfilled.Sum(tp => tp.Amount);
          if (remainingPosition < totalTpUnfilledAmount) {
            Log($"Adjusting TakeProfit ladder due to smaller remaining position: {remainingPosition} vs unfilled TP: {totalTpUnfilledAmount}");
            EnqueueCancelOrdersWithProperty("TakeProfitClosePosition", "Adjusting TakeProfit ladder for remaining position");

            // Adjust the last few orders to match remaining position and ensure minimum order value
            decimal remainingToClose = remainingPosition;
            var newTpLaddering = new List<ExitLadderInfo>();
            decimal minOrderSize = (decimal)(_minimumOrderSize / _trade);

            for (int i = 0; i < tpUnfilled.Count && remainingToClose > 0; i++) {
              var currentStep = tpUnfilled[i];
              decimal orderSize = Math.Min(currentStep.Amount, remainingToClose);
              if (orderSize * _trade < _minimumOrderSize && i == tpUnfilled.Count - 1) {
                // Last order, need to merge with previous if possible
                if (newTpLaddering.Count > 0) {
                  var lastAdded = newTpLaddering[newTpLaddering.Count - 1];
                  newTpLaddering[newTpLaddering.Count - 1] = new ExitLadderInfo {
                    Price = lastAdded.Price,
                    Amount = lastAdded.Amount + (position > 0 ? -orderSize : orderSize),
                  };
                  remainingToClose -= orderSize;
                }
              } else if (orderSize * _trade >= _minimumOrderSize) {
                newTpLaddering.Add(new ExitLadderInfo {
                  Price = currentStep.Price,
                  Amount = position > 0 ? -orderSize : orderSize,
                });
                remainingToClose -= orderSize;
              }
            }

            _tpLaddering = newTpLaddering.ToArray();
            for (int i = 0; i < _tpLaddering.Length; i++)
              _pendingOrders.Enqueue(("TakeProfitClosePosition", i, 0, _tpLaddering[i].Amount, _tpLaddering[i].Price, false, "Adjusted TakeProfit ladder"));
          }
        }
      }
    }

    private void PlaceNextOrder(string reason, string property, int ladderInx, int entryInx, decimal quantity, decimal price, bool isStopLimit) {
      var currentPosition = Portfolio[_qcSymbol].Quantity;

      // The order that was intended to be canceled might have been filled before the cancellation succeeded, so retrieve the latest position quantity before placing a new order.
      if (!_testing.IsTesting && (property == "StopLossClosePosition" || property == "TakeProfitClosePosition")) {
        if (currentPosition == 0) {
          Log($"Skipping {property} order, QTY={quantity}, ladderInx={ladderInx}, entryInx={entryInx}, reason={reason}, no position to close. Current position: {currentPosition}");
          CurrentOrder = null;
          return;
        }

        var maxCloseQuantity = Math.Min(Math.Abs(quantity), Math.Abs(currentPosition));
        var adjustedQuantity = currentPosition > 0 ? -maxCloseQuantity : maxCloseQuantity;
        if (Math.Abs(adjustedQuantity) < Math.Abs(quantity)) {
          Log($"Adjusted {property} order quantity from {quantity} to {adjustedQuantity} based on current position {currentPosition}");
          quantity = adjustedQuantity;
        }

        if (Math.Abs(quantity * price) < _minimumOrderSize) {
          Log($"Skipping {property} order - adjusted quantity {quantity} * price {price} = {Math.Abs(quantity * price)} below minimum order size {_minimumOrderSize}");
          CurrentOrder = null;
          return;
        }
      }

      var tagDict = new Dictionary<string, object> {
        { "barIdx", _barIndex },
        { "LadderInx", ladderInx },
        { "property", property },
        { "SubmitTime", GetUtcTime() },
        { "Reason", reason },
      };

      if (property == "OpenPosition") {
        tagDict["entryIdx"] = entryInx;
        tagDict["NextOrderSize"] = Math.Abs(quantity);
        tagDict["InitPosition"] = Portfolio[_qcSymbol].Quantity;
        tagDict["entryPrice"] = price;
      }

      OrderTicket ticket;
      if (isStopLimit) {
        var limitPrice = price;
        var position = Portfolio[_qcSymbol].Quantity;

        var stopMultiplier = 1;

        var stopGap = stopMultiplier * _symbolProperties.MinimumPriceVariation;
        var stopPrice = position > 0 ? limitPrice - stopGap : limitPrice + stopGap;

        if (LiveMode)
          ticket = StopLimitOrder(_qcSymbol, quantity, stopPrice, limitPrice, JsonConvert.SerializeObject(tagDict));
        else
          ticket = StopMarketOrder(_qcSymbol, quantity, stopPrice, JsonConvert.SerializeObject(tagDict));
      } else {
        var orderProperties = new BinanceOrderProperties { PostOnly = true };
        ticket = LimitOrder(_qcSymbol, quantity, price, JsonConvert.SerializeObject(tagDict), orderProperties);
      }

      CurrentOrder = new OrderState {
        Ticket = ticket,
        Property = property,
        LadderIndex = ladderInx,
        EntryIndex = entryInx,
        SubmitTime = DateTime.UtcNow,
        LastStatus = ticket.Status
      };

      if (property == "OpenPosition") {
        Log($"barIdx={_barIndex}, entryIdx={entryInx}, OpenPosition, OID={ticket.OrderId}, Status={ticket.Status}, QTY={ticket.Quantity}, Tag=`{ticket.Tag}` [SET AS CurrentOrder]");
      } else {
        Log($"barIdx={_barIndex}, OID={ticket.OrderId}, Status={ticket.Status}, QTY={ticket.Quantity}, Tag=`{ticket.Tag}` [SET AS CurrentOrder]");
      }
    }

    private void EnqueueCancelAllOrders(string reason) {
      var openOrders = Transactions.GetOpenOrders();
      foreach (var order in openOrders) {
        Dictionary<string, object> orderTagDict;
        try {
          orderTagDict = JsonConvert.DeserializeObject<Dictionary<string, object>>(order.Tag) ?? new Dictionary<string, object>();
        } catch (Exception ex) {
          Log($"Error deserializing tag '{order.Tag}' for order {order.Id} during EnqueueCancelAllOrders: {ex.Message}");
          orderTagDict = new Dictionary<string, object>();
        }

        orderTagDict["Reason"] = reason;
        _pendingCancelRequests.Enqueue((order.Id, orderTagDict, reason));
      }
      if (openOrders.Count > 0)
        Log($"Enqueued cancellation for {openOrders.Count} orders: {reason}");
    }

    private void ResetLadders() {
      _tpLaddering = new ExitLadderInfo[(int)_tradingParams["tp_levels"]];
      _slLaddering = new ExitLadderInfo[(int)_tradingParams["sl_levels"]];
      _pendingStopLossAfterTP = null;
    }

    private bool PrecheckLaddering(decimal startPrice, decimal endPrice, int nLevels) {
      if (nLevels <= 0)
        return false;

      if (Math.Abs(startPrice - endPrice) < 1e-9m && nLevels > 1) {
        Log($"Precheck laddering failed: start_price ({startPrice.ToString(_priceDecimalFormat)}) and end_price ({endPrice.ToString(_priceDecimalFormat)}) too close for {nLevels} levels");
        return false;
      }

      var diffUnit = (endPrice - startPrice) / nLevels;
      var firstLevelPrice = startPrice + diffUnit;
      var exponent = Math.Max(0, (int)-Math.Log10((double)_symbolProperties.MinimumPriceVariation));
      firstLevelPrice = Math.Round(firstLevelPrice, exponent);

      var actualGap = Math.Abs(firstLevelPrice - startPrice);
      var isValid = actualGap >= _minLadderGap;

      Log($"Precheck laddering {(isValid ? "Passed" : "Failed")}: actual_gap ({actualGap.ToString(_priceDecimalFormat)}) {(isValid ? ">=" : "<")} required_gap ({_minLadderGap.ToString(_priceDecimalFormat)}), start_price={startPrice.ToString(_priceDecimalFormat)}, end_price={endPrice.ToString(_priceDecimalFormat)}, n_levels={nLevels}");
      return isValid;
    }

    private ExitLadderInfo[] PopulateLaddering(decimal position, decimal startPrice, decimal endPrice, int nLevels, ExitLadderInfo[] laddering, bool isStopLoss) {
      if (position == 0 || startPrice <= 0 || endPrice <= 0 || Math.Abs(startPrice - endPrice) / nLevels <= _symbolProperties.MinimumPriceVariation)
        throw new Exception("Invalid laddering parameters");

      var granularityCnt = Math.Abs(position) / _symbolProperties.LotSize;
      if (granularityCnt < 0.00004m)
        throw new Exception("Position too small for granularity");

      var pictLaddering = GetIntervalLists(nLevels);
      var newLaddering = new ExitLadderInfo[nLevels];
      var diffUnit = (endPrice - startPrice) / nLevels;

      if (Math.Abs(diffUnit) <= _symbolProperties.MinimumPriceVariation)
        throw new Exception($"Diff unit ({diffUnit}) too small compared to laddering_diff ({_symbolProperties.MinimumPriceVariation})");

      decimal totalAssigned = 0;
      var precision = _symbolProperties.LotSize < 1 ? (int)-Math.Log10((double)_symbolProperties.LotSize) : 0;
      var exponent = Math.Max(0, (int)-Math.Log10((double)_symbolProperties.MinimumPriceVariation));

      for (int inx = 0; inx < nLevels; inx++) {
        var ladderInxPrice = startPrice + (inx + 1) * diffUnit;
        newLaddering[inx].Price = Math.Round(ladderInxPrice, exponent);

        decimal orderSize;
        if (inx < nLevels - 1)
          orderSize = Math.Round(pictLaddering[inx] / 100 * position, precision);
        else
          orderSize = Math.Round(position - totalAssigned, precision);

        totalAssigned += orderSize;
        newLaddering[inx].Amount = orderSize;

        var orderValue = Math.Abs(newLaddering[inx].Price * newLaddering[inx].Amount);
        if (orderValue <= _minimumOrderSize)
          throw new Exception($"Order value below minimum: {orderValue.ToString(_priceDecimalFormat)} <= {_minimumOrderSize.ToString(_positionDecimalFormat)} (Price: {newLaddering[inx].Price.ToString(_priceDecimalFormat)}, Amount: {newLaddering[inx].Amount.ToString(_positionDecimalFormat)}, Ladder Index: {inx})");
      }

      return newLaddering;
    }

    private List<decimal> GetIntervalLists(decimal numParts) {
      var result = new List<decimal>();
      for (decimal i = 0m; i < numParts; i++)
        result.Add(100m / numParts);
      return result;
    }

    private void LogVbt(DateTime tic, TradeBar bar, string barStartTime, int signalValue) {
      var costSeconds = (DateTime.UtcNow - tic).TotalSeconds;
      var vbtString = $"`{barStartTime}, {bar.Open.ToString(_priceDecimalFormat)}, {bar.High.ToString(_priceDecimalFormat)}, {bar.Low.ToString(_priceDecimalFormat)}, {bar.Close.ToString(_priceDecimalFormat)}, {bar.Volume.ToString(_positionDecimalFormat)}, {signalValue}`";
      var position = Portfolio[_qcSymbol].Quantity;
      var cashBalance = Portfolio.CashBook[_symbolProperties.QuoteCurrency].Amount;
      var cryptoBalance = Portfolio.CashBook[_cryptoSymbol].Amount;
      Log($"barIdx={_barIndex}, opening={_openingPosition}, position={position.ToString(_positionDecimalFormat)}, pendingOrders={Transactions.GetOpenOrders(_qcSymbol).Count}, {_symbolProperties.QuoteCurrency}={cashBalance:F2}, crypto={cryptoBalance.ToString(_positionDecimalFormat)}, cost={costSeconds:F3}s, vbt: {vbtString}");
    }

    private void HandleEmergencyStopLossRefresh(TradeBar bar, List<Order> openOrders, decimal position) {
      if (CurrentOrder != null) {
        Log($"Skipping emergency SL refresh: Active order in progress. (CurrentOrder: OID={CurrentOrder.Ticket.OrderId})");
        return;
      }

      var stopLossOrders = new List<(Order, Dictionary<string, object>)>();
      foreach (var loopOrder in openOrders) {
        try {
          var orderTagDict = JsonConvert.DeserializeObject<Dictionary<string, object>>(loopOrder.Tag);
          if (orderTagDict != null && orderTagDict.ContainsKey("property") && orderTagDict["property"].ToString() == "StopLossClosePosition")
            stopLossOrders.Add((loopOrder, orderTagDict));
        } catch (Exception ex) {
          Log($"Error decoding tag for order {loopOrder.Id}: '{loopOrder.Tag}'. Error: {ex.Message}");
        }
      }

      if (stopLossOrders.Count != 1) { // only when there is one and only one StopLossClosePosition Open Order shall we handle it
        return;
      }

      var order = stopLossOrders[stopLossOrders.Count - 1].Item1;
      var tagDict = stopLossOrders[stopLossOrders.Count - 1].Item2;
      // if (tagDict.TryGetValue("bar_inx", out var barInxObj) && int.TryParse(barInxObj?.ToString(), out int barInx) && _barIndex - barInx < 50) {
      //   return;
      // }

      var stopLimitOrder = Transactions.GetOrderById(order.Id) as StopLimitOrder;
      if (stopLimitOrder == null) {
        Log($"Order {order.Id} is not a StopLimitOrder. Skipping refresh check.");
        return;
      }

      var stopPrice = stopLimitOrder.StopPrice;
      var limitPrice = stopLimitOrder.LimitPrice;

      bool priceTooFar =
        // (position < 0 && (bar.Close < limitPrice - _atr.Current.Value /*|| bar.Close > limitPrice + _atr.Current.Value*/)) ||
        (position > 0 && (bar.Close > limitPrice + _atr.Current.Value /*|| bar.Close < limitPrice - _atr.Current.Value*/));

      if (priceTooFar) {
        Log($"Detected potentially stale StopLoss order {order.Id} at barIdx {_barIndex}. Bid={_bid!.Value.ToString(_priceDecimalFormat)}, Ask={_ask!.Value.ToString(_priceDecimalFormat)}, LimitPrice: {limitPrice.ToString(_priceDecimalFormat)}, Position: {position}");

        tagDict["Reason"] = $"Canceling stale StopLoss for refresh at barIdx {_barIndex}";
        _pendingCancelRequests.Enqueue((order.Id, tagDict, tagDict["Reason"].ToString()!));

        EnqueueCancelAllOrders("priceTooFar, clearing all stale orders to place new protective TakeProfit/StopLoss Orders");
      }
    }

    private void BarHandler(object? sender, TradeBar bar) {
      var tic = DateTime.UtcNow;
      _barIndex++;
      _rollingConsolidatedBar.Add(bar);
      //if (0 == _barIndex) {
      //  _testing?.PlaceTestOrders();
      //  _testing?.ScheduleCancelAllOrdersAfter_1_Minutes();
      //}
      var barStartTime = bar.Time.ToString("yyyy-MM-dd HH:mm:ss");

      var openOrders = Transactions.GetOpenOrders(_qcSymbol);
      foreach (var order in openOrders) {
        try {
          var tagDict = JsonConvert.DeserializeObject<Dictionary<string, object>>(order.Tag) ?? new Dictionary<string, object>();

          if (tagDict.TryGetValue("property", out var propObj) && propObj?.ToString() == "OpenPosition" &&
              tagDict.TryGetValue("barIdx", out var barInxObj) && int.TryParse(barInxObj?.ToString(), out int orderBarInx) &&
              _barIndex - orderBarInx > 0) {
            tagDict["Reason"] = $"Unfilled OpenPosition order from barIdx {orderBarInx} canceled at barIdx {_barIndex}";
            _pendingCancelRequests.Enqueue((order.Id, tagDict, tagDict["Reason"]?.ToString() ?? "Unfilled OpenPosition order canceled"));
            _openingPosition = 0;
            Log(tagDict["Reason"].ToString());
          }
        } catch (Exception ex) {
          Log($"Error deserializing tag '{order.Tag}' for order {order.Id} during BarHandler open order check: {ex.Message}");
        }
      }

      var signalResult = _signalGenerator.GenerateSignal(bar, _barIndex);
      int totalSignal = (int)signalResult.Signal;

      totalSignal = 0;
      if (_random.Next(0, 2) == 0)
        totalSignal = 2;

      LogVbt(tic, bar, barStartTime, totalSignal);

      if (totalSignal == 0 || Portfolio.Invested || Transactions.GetOpenOrders(_qcSymbol).Count > 0 || _openingPosition != 0)
        return;

      bool isLong = totalSignal == 2;
      _openingPosition = totalSignal;
      var freeCashPct = isLong ? 1.0m : -1.0m;
      _entryLaddering = new EntryLadderInfo[(int)_tradingParams["n_entries"]];

      for (int i = 0; i < _tradingParams["n_entries"]; i++) {
        _entryLaddering[i] = new EntryLadderInfo {
          BarIndex = _barIndex + (int)_tradingParams["num_step_entries"] * i,
          AssetValuePct = freeCashPct / (_tradingParams["n_entries"] - i)
        };
      }

      ProcessEntry(bar);
    }

    private void ProcessEntry(TradeBar bar) {
      var entryIdx = Array.FindIndex(_entryLaddering, e => e.BarIndex == _barIndex);
      if (entryIdx == -1 || CurrentOrder != null)
        return; // Only process if no active order

      if (entryIdx == 0 && Portfolio.Invested)
        throw new Exception("Portfolio already invested at first entry");

      var assetValuePct = _entryLaddering[entryIdx].AssetValuePct;
      var adjustment = _symbolProperties.MinimumPriceVariation * 2;
      var valPrice = assetValuePct > 0 ? _bid!.Value - adjustment : _ask!.Value + adjustment;
      _entryLaddering[entryIdx].Price = valPrice;

      var n = _barIndex - _entryLaddering[0].BarIndex + 1;
      var avgAtr = _atr.Samples > 0 ? _atr.Take((int)Math.Min(n, _atr.Samples)).Select(idp => idp.Value).Average() : 0;
      var avgClose = _rollingConsolidatedBar.Take(Math.Min(n, _rollingConsolidatedBar.Count)).Average(b => b.Close);
      var initPosition = Portfolio[_qcSymbol].Quantity;

      var cashBalance = Portfolio.CashBook[_symbolProperties.QuoteCurrency].Amount;
      var orderCash = Math.Abs(_mySize * cashBalance * _myLeverage * assetValuePct);
      var orderSize = (orderCash / (valPrice * (1 + _myFees)));
      var orderSizePrecision = _symbolProperties.LotSize < 1 ? (int)-Math.Log10((double)_symbolProperties.LotSize) : 0;
      decimal multiplier = (decimal)Math.Pow(10, orderSizePrecision);
      orderSize = Math.Truncate(orderSize * multiplier) / multiplier;
      if (orderSize <= 0) {
        _openingPosition = 0;
        return;
      }

      _entryLaddering[entryIdx].Cost = valPrice * orderSize;

      var accumulatedCost = _entryLaddering.Take(entryIdx + 1).Sum(e => e.Cost);
      var entryPrice = Math.Abs(accumulatedCost / (orderSize != 0 ? orderSize : 1));

      var maxPriceDiff = Math.Abs(_mySize * _myLeverage * cashBalance / orderSize);
      decimal longStopLoss = decimal.MaxValue;
      decimal longTakeProfit = decimal.MaxValue;
      decimal shortStopLoss = decimal.MaxValue;
      decimal shortTakeProfit = decimal.MaxValue;

      if (_openingPosition == 2) {
        longStopLoss = Math.Max(entryPrice - maxPriceDiff, avgClose - _tradingParams["atr_sl_ratio"] * avgAtr);
        longTakeProfit = avgClose + _tradingParams["atr_tp_ratio"] * avgAtr;
        if (longStopLoss < 0 || entryPrice <= longStopLoss || Math.Abs(entryPrice - longStopLoss) / _tradingParams["sl_levels"] <= _symbolProperties.MinimumPriceVariation ||
            Math.Abs(entryPrice - longTakeProfit) / _tradingParams["tp_levels"] <= _symbolProperties.MinimumPriceVariation) {
          _openingPosition = 0;
          return;
        }
      } else {
        shortStopLoss = Math.Min(entryPrice + maxPriceDiff, avgClose + _tradingParams["atr_sl_ratio"] * avgAtr);
        shortTakeProfit = avgClose - _tradingParams["atr_tp_ratio"] * avgAtr;
        if (shortTakeProfit < 0 || shortStopLoss <= entryPrice || Math.Abs(entryPrice - shortStopLoss) / _tradingParams["sl_levels"] <= _symbolProperties.MinimumPriceVariation ||
            Math.Abs(entryPrice - shortTakeProfit) / _tradingParams["tp_levels"] <= _symbolProperties.MinimumPriceVariation) {
          _openingPosition = 0;
          return;
        }
      }

      var stopLoss = _openingPosition == 2 ? longStopLoss : shortStopLoss;
      var takeProfit = _openingPosition == 2 ? longTakeProfit : shortTakeProfit;
      var slValid = PrecheckLaddering(entryPrice, stopLoss, (int)_tradingParams["sl_levels"]);
      var tpValid = PrecheckLaddering(entryPrice, takeProfit, (int)_tradingParams["tp_levels"]);
      if (!slValid || !tpValid) {
        Log($"Laddering setup aborted: SL_valid={slValid}, TP_valid={tpValid}, SL={stopLoss.ToString(_priceDecimalFormat)}, TP={takeProfit.ToString(_priceDecimalFormat)}, entryPrice={entryPrice.ToString(_priceDecimalFormat)}");
        _openingPosition = 0;
        return;
      }

      var orderProperties = new BinanceOrderProperties { PostOnly = true };
      var quantity = orderSize * (assetValuePct < 0 ? -1 : 1);
      quantity = Math.Truncate(quantity * multiplier) / multiplier;

      CurrentOrder = null; // Ensure no active order is set, allowing queue processing
      Log($"barIdx={_barIndex}, entryIdx={entryIdx}, Enqueued OpenPosition order, QTY={quantity}, price={valPrice.ToString(_priceDecimalFormat)}");

      _pendingOrders.Enqueue(("OpenPosition", 0, entryIdx, quantity, valPrice, false, $"Entry order {entryIdx + 1} of {_tradingParams["n_entries"]}")); // Quantity and price calculated later
    }

    private string GetUtcTime() {
      return DateTime.UtcNow.ToString("yyyy-MM-dd HH:mm:ss.fff");
    }

    private struct EntryLadderInfo {
      public int BarIndex;
      public decimal Price;
      public decimal AssetValuePct;
      public decimal Cost;
      public decimal PositionChanged;
    }

    private struct ExitLadderInfo {
      public decimal Price;
      public decimal Amount;
    }

    private void EnqueueCancelOrdersWithProperty(string property, string reason) {
      var openOrders = Transactions.GetOpenOrders();
      foreach (var order in openOrders) {
        if (!string.IsNullOrEmpty(order.Tag) && order.Tag.Contains(property)) {
          var orderTagDict = JsonConvert.DeserializeObject<Dictionary<string, object>>(order.Tag) ?? new Dictionary<string, object>();
          orderTagDict["Reason"] = reason;
          _pendingCancelRequests.Enqueue((order.Id, orderTagDict, reason));
        }
      }
      if (openOrders.Count > 0)
        Log($"Enqueued cancellation for {openOrders.Count} orders with property {property}: {reason}");
    }

    private void ClearOrdersByPropertyFromQueue(string propertyToRemove) {
      var remainingOrders = _pendingOrders.Where(order => order.Property != propertyToRemove).ToList();
      _pendingOrders.Clear();
      foreach (var order in remainingOrders) {
        _pendingOrders.Enqueue(order);
      }
    }

    public void EnqueueTestOrder(string property, int ladderIndex, int entryIndex, decimal quantity, decimal price, bool isStopLimit, string reason) {
      _pendingOrders.Enqueue((property, ladderIndex, entryIndex, quantity, price, isStopLimit, reason));
      Log($"Enqueued test order: {property}, QTY={quantity}, Price={price.ToString(_priceDecimalFormat)}, IsStopLimit={isStopLimit}, Reason={reason}");
    }
  }

  public class VillaFeeModel: FeeModel {
    public override OrderFee GetOrderFee(OrderFeeParameters parameters) {
      var order = parameters.Order;
      var security = parameters.Security;
      var orderValue = Math.Abs(order.Quantity * security.Price);

      if (!string.IsNullOrEmpty(order.Tag) && order.Tag.Contains("NextOrderSize"))
        return new OrderFee(new CashAmount(0, security.QuoteCurrency.Symbol));

      var feeAmount = 0m; // Maker fees set to 0
      return new OrderFee(new CashAmount(feeAmount, security.QuoteCurrency.Symbol));
    }
  }

  public class CustomCryptoData: Tick {
    public CustomCryptoData() {
    }

    public CustomCryptoData(Symbol symbol, DateTime time, decimal value, decimal quantity, TickType tickType = TickType.Trade, decimal bidPrice = 0, decimal askPrice = 0) {
      Symbol = symbol;
      Time = time;
      Value = value;
      Quantity = quantity;
      TickType = tickType;
      if (tickType == TickType.Quote) {
        BidPrice = bidPrice;
        AskPrice = askPrice;
      }
      Exchange = "Binance";
    }

    public override SubscriptionDataSource GetSource(SubscriptionDataConfig config, DateTime date, bool isLiveMode) {
      var tickTypeString = config.TickType.ToString().ToLower();
      var formattedDate = date.ToString("yyyyMMdd");
      var source = Path.Combine(Globals.DataFolder, "crypto", "binance", "tick", config.Symbol.Value.ToLower(), $"{formattedDate}_{tickTypeString}.zip");
      return new SubscriptionDataSource(source, SubscriptionTransportMedium.LocalFile, FileFormat.Csv);
    }

    public override BaseData? Reader(SubscriptionDataConfig config, string line, DateTime date, bool isLiveMode) {
      if (string.IsNullOrWhiteSpace(line) || !char.IsDigit(line[0])) {
        return null;
      }

      var data = line.Split(',');
      try {
        var price = Convert.ToDecimal(data[1]);
        var quantity = Convert.ToDecimal(data[2]);
        var timestampMicroseconds = long.Parse(data[4]);
        long ticks = timestampMicroseconds * 10; // Convert microseconds to ticks (1 microsecond = 10 ticks)
        DateTime epoch = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc);
        DateTime dataDateTime = epoch.AddTicks(ticks);

        TickType tickType = data.Length > 5 && data[5].Contains("quote") ? TickType.Quote : TickType.Trade;
        decimal bidPrice = tickType == TickType.Quote ? price : 0;
        decimal askPrice = tickType == TickType.Quote ? Convert.ToDecimal(data[3]) : 0; // Assuming ask price is in data[3] for quotes

        var tick = new CustomCryptoData(config.Symbol, dataDateTime, price, quantity, tickType, bidPrice, askPrice);
        return tick;
      } catch (Exception) {
        return null;
      }
    }
  }
}